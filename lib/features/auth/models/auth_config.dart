/// Authentication Configuration Constants
/// Contains all authentication-related URLs and configuration values
class AuthConfig {
  // API URLs
  static const String au1Url = 'https://api.nudron.com/prod/au1';
  static const String au3Url = 'https://api.nudron.com/prod/au3';
  
  // Client Configuration
  static const String tenantId = "d14b3819-5e90-4b1e-8821-9fcb72684627";
  static const String clientId = "WaterMeteringMobile2";
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String emailKey = 'email';
  static const String passwordKey = 'password';
  static const String twoFactorKey = 'two_factor';
  static const String biometricKey = 'biometric';
  static const String themeModeKey = 'themeMode';
  
  // Token Configuration
  static const Duration tokenRefreshThreshold = Duration(minutes: 30);
  static const Duration requestTimeout = Duration(seconds: 5);
  static const Duration tokenRefreshDelay = Duration(seconds: 10);
  
  // Password Requirements
  static const int minimumPasswordLength = 8;
  
  // Two-Factor Authentication Modes
  static const int twoFAModeSMS = 2;
  static const int twoFAModeAppLight = 10;
  static const int twoFAModeAppDark = 11;
}
