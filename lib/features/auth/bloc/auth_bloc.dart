import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';

import '../service/auth_service.dart';
import '../service/biometric_service.dart';
import '../repository/auth_repository.dart';
import '../../../view_model/custom_exception.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// Authentication BLoC
/// Manages authentication state and handles authentication events
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final BiometricService _biometricService = BiometricService();

  AuthBloc() : super(const AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthBiometricLoginRequested>(_onAuthBiometricLoginRequested);
    on<AuthTwoFactorRequested>(_onAuthTwoFactorRequested);
    on<AuthResendOtpRequested>(_onAuthResendOtpRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthGlobalLogoutRequested>(_onAuthGlobalLogoutRequested);
    on<AuthSignupRequested>(_onAuthSignupRequested);
    on<AuthContactVerificationRequested>(_onAuthContactVerificationRequested);
    on<AuthForgotPasswordRequested>(_onAuthForgotPasswordRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthEnableTwoFactorRequested>(_onAuthEnableTwoFactorRequested);
    on<AuthDisableTwoFactorRequested>(_onAuthDisableTwoFactorRequested);
  }

  /// Check if user is already authenticated
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final accessToken = await AuthService.getAccessToken2();
      if (accessToken != null) {
        final userEmail = await AuthRepository.getUserEmail();
        final userProfile = await AuthService.tokenCheck();

        AuthService.isLoggedIn = true;
        emit(AuthAuthenticated(
          accessToken: accessToken,
          userEmail: userEmail,
          userProfile: userProfile,
        ));
      } else {
        AuthService.isLoggedIn = false;
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      AuthService.isLoggedIn = false;
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle login request
  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final twoFactorRefCode =
          await AuthService.login(event.email, event.password);

      if (twoFactorRefCode != null) {
        // Two-factor authentication required
        emit(AuthTwoFactorRequired(referenceCode: twoFactorRefCode));
      } else {
        // Login successful
        final accessToken = await AuthService.getAccessToken2();
        final userProfile = await AuthService.tokenCheck();

        AuthService.isLoggedIn = true;
        emit(AuthAuthenticated(
          accessToken: accessToken!,
          userEmail: event.email,
          userProfile: userProfile,
        ));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle biometric login request
  Future<void> _onAuthBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final password = await _biometricService.getPassword();
      final twoFactorRefCode = await AuthService.login(event.email, password);

      if (twoFactorRefCode != null) {
        // Two-factor authentication required
        emit(AuthTwoFactorRequired(referenceCode: twoFactorRefCode));
      } else {
        // Login successful
        final accessToken = await AuthService.getAccessToken2();
        final userProfile = await AuthService.tokenCheck();

        AuthService.isLoggedIn = true;
        emit(AuthAuthenticated(
          accessToken: accessToken!,
          userEmail: event.email,
          userProfile: userProfile,
        ));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle two-factor authentication
  Future<void> _onAuthTwoFactorRequested(
    AuthTwoFactorRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      await AuthService.sendTwoFactorCode(
          event.referenceCode, event.twoFactorCode);

      final accessToken = await AuthService.getAccessToken2();
      final userEmail = await AuthRepository.getUserEmail();
      final userProfile = await AuthService.tokenCheck();

      AuthService.isLoggedIn = true;
      emit(AuthAuthenticated(
        accessToken: accessToken!,
        userEmail: userEmail,
        userProfile: userProfile,
      ));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle resend OTP request
  Future<void> _onAuthResendOtpRequested(
    AuthResendOtpRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      await AuthService.resendOtp(event.referenceCode);

      emit(const AuthOtpResent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle token refresh request
  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthTokenRefreshing());

      final newAccessToken = await AuthService.getNewAccessToken();
      final userEmail = await AuthRepository.getUserEmail();
      final userProfile = await AuthService.tokenCheck();

      emit(AuthAuthenticated(
        accessToken: newAccessToken,
        userEmail: userEmail,
        userProfile: userProfile,
      ));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle logout request
  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoggingOut());

      await AuthService.logout();
      AuthService.isLoggedIn = false;

      emit(const AuthLogoutSuccess());
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails on server, clear local data
      await AuthService.deleteDataAndLogout();
      AuthService.isLoggedIn = false;
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle global logout request
  Future<void> _onAuthGlobalLogoutRequested(
    AuthGlobalLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoggingOut());

      await AuthService.globalLogout();
      AuthService.isLoggedIn = false;

      emit(const AuthLogoutSuccess());
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails on server, clear local data
      await AuthService.deleteDataAndLogout();
      AuthService.isLoggedIn = false;
      emit(const AuthUnauthenticated());
    }
  }

  /// Handle signup request
  Future<void> _onAuthSignupRequested(
    AuthSignupRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final timestamp = await AuthService.signUp(
        event.activationCode,
        event.fullName,
        event.email,
        event.phone,
      );

      emit(AuthSignupSuccess(timestamp: timestamp));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle contact verification request
  Future<void> _onAuthContactVerificationRequested(
    AuthContactVerificationRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final response = await AuthService.contactVerification(
        event.activationCode,
        event.password,
        event.emailCode,
        event.phoneCode,
      );

      emit(AuthContactVerificationSuccess(response: response));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle forgot password request
  Future<void> _onAuthForgotPasswordRequested(
    AuthForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final timestamp = await AuthService.forgotPassword(event.email);

      emit(AuthForgotPasswordSuccess(timestamp: timestamp));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle password reset request
  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      await AuthService.resetPassword(
          event.email, event.newPassword, event.resetCode);

      emit(const AuthPasswordResetSuccess());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle enable two-factor authentication request
  Future<void> _onAuthEnableTwoFactorRequested(
    AuthEnableTwoFactorRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      final qrData = await AuthService.enableTwoFactorAuth(event.mode);

      emit(AuthTwoFactorEnabled(qrData: qrData));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Handle disable two-factor authentication request
  Future<void> _onAuthDisableTwoFactorRequested(
    AuthDisableTwoFactorRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      await AuthService.disableTwoFactorAuth();

      emit(const AuthTwoFactorDisabled());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }
}
